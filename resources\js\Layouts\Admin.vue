<script setup>
import { ref } from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import { Link, usePage } from '@inertiajs/vue3';

const user = usePage().props.auth?.user;
const showingNavigationDropdown = ref(false);
</script>

<template>
    <div>
        <div class="min-h-screen bg-gradient-to-br from-slate-50 via-red-50 to-rose-50">
            <!-- Navigation moderne -->
            <nav class="bg-white/80 backdrop-blur-sm border-b border-slate-200/50 sticky top-0 z-50">
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div class="flex h-16 justify-between items-center">
                        <div class="flex items-center gap-8">
                            <!-- Logo moderne -->
                            <div class="flex items-center gap-3">
                                <div class="bg-gradient-to-br from-red-600 via-rose-700 to-red-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg shadow-red-500/20">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                                        Administration
                                    </h1>
                                    <p class="text-xs text-slate-500 font-medium">Panneau de contrôle</p>
                                </div>
                            </div>

                            <!-- Navigation Links modernes -->
                            <div class="hidden lg:flex items-center space-x-1">
                                <Link :href="route('admin.dashboard')"
                                      :class="[
                                          'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300',
                                          route().current('admin.dashboard')
                                              ? 'bg-red-100 text-red-800 shadow-sm'
                                              : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                                      ]">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v3H8V5z"/>
                                    </svg>
                                    Dashboard
                                </Link>
                                <Link :href="route('admin.users.index')"
                                      :class="[
                                          'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300',
                                          route().current('admin.users.*')
                                              ? 'bg-red-100 text-red-800 shadow-sm'
                                              : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                                      ]">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                    </svg>
                                    Utilisateurs
                                </Link>
                                <Link :href="route('admin.structures.index')"
                                      :class="[
                                          'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300',
                                          route().current('admin.structures.*')
                                              ? 'bg-red-100 text-red-800 shadow-sm'
                                              : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                                      ]">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                    Structures
                                </Link>
                                <Link :href="route('admin.stagiaires.index')"
                                      :class="[
                                          'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300',
                                          route().current('admin.stagiaires.*')
                                              ? 'bg-red-100 text-red-800 shadow-sm'
                                              : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                                      ]">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                                    </svg>
                                    Stagiaires
                                </Link>
                                <Link :href="route('admin.agents.index')"
                                      :class="[
                                          'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300',
                                          route().current('admin.agents.*')
                                              ? 'bg-red-100 text-red-800 shadow-sm'
                                              : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                                      ]">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"/>
                                    </svg>
                                    Agents
                                </Link>
                            </div>
                        </div>

                        <!-- Profil utilisateur moderne -->
                        <div class="hidden sm:flex sm:items-center sm:gap-4">
                            <!-- Notifications -->
                            <div class="relative">
                                <button class="p-2 text-slate-400 hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"/>
                                    </svg>
                                </button>
                            </div>

                            <!-- Dropdown profil -->
                            <div class="relative">
                                <Dropdown align="right" width="56">
                                    <template #trigger>
                                        <button class="flex items-center gap-3 px-3 py-2 bg-red-50 hover:bg-red-100 rounded-xl transition-all duration-300">
                                            <div class="relative">
                                                <img v-if="user && user.avatar"
                                                     :src="'/storage/' + user.avatar"
                                                     alt="Photo de profil"
                                                     class="w-8 h-8 rounded-xl object-cover border-2 border-red-200"/>
                                                <div v-else class="w-8 h-8 rounded-xl bg-gradient-to-br from-red-500 to-rose-600 flex items-center justify-center text-white text-sm font-bold border-2 border-red-200">
                                                    {{ user?.nom?.charAt(0) || 'A' }}
                                                </div>
                                                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                                            </div>
                                            <div class="text-left hidden lg:block">
                                                <div class="text-sm font-bold text-slate-800">{{ user?.nom || 'Admin' }}</div>
                                                <div class="text-xs text-red-600 font-medium">Administrateur</div>
                                            </div>
                                            <svg class="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                            </svg>
                                        </button>
                                    </template>

                                    <template #content>
                                        <div class="p-2">
                                            <Link :href="route('profile.edit')"
                                                  class="flex items-center gap-3 px-4 py-3 text-sm text-slate-700 hover:bg-red-50 rounded-xl transition-all duration-300">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                                Mon Profil
                                            </Link>
                                            <hr class="my-2 border-slate-200">
                                            <Link :href="route('logout')" method="post" as="button"
                                                  class="flex items-center gap-3 w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-xl transition-all duration-300">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                                </svg>
                                                Déconnexion
                                            </Link>
                                        </div>
                                    </template>
                                </Dropdown>
                            </div>
                        </div>

                        <!-- Hamburger -->
                        <div class="-me-2 flex items-center sm:hidden">
                            <button @click="
                                    showingNavigationDropdown =
                                        !showingNavigationDropdown
                                "
                                class="inline-flex items-center justify-center rounded-md p-2 text-gray-400 transition duration-150 ease-in-out hover:bg-gray-100 hover:text-gray-500 focus:bg-gray-100 focus:text-gray-500 focus:outline-none">
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path :class="{
                                            hidden: showingNavigationDropdown,
                                            'inline-flex':
                                                !showingNavigationDropdown,
                                        }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16" />
                                    <path :class="{
                                            hidden: !showingNavigationDropdown,
                                            'inline-flex':
                                                showingNavigationDropdown,
                                        }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Responsive Navigation Menu -->
                <div :class="{
                        block: showingNavigationDropdown,
                        hidden: !showingNavigationDropdown,
                    }" class="sm:hidden">
                    <div class="space-y-1 pb-3 pt-2">
                        <ResponsiveNavLink :href="route('admin.dashboard')" :active="route().current('admin.dashboard')">
                            Tableau de bord
                        </ResponsiveNavLink>

                        <ResponsiveNavLink :href="route('admin.users.index')" :active="route().current('admin.users.index')">
                            Users
                        </ResponsiveNavLink>

                        <ResponsiveNavLink :href="route('admin.structures.index')" :active="route().current('admin.structures.index')">
                            Structures
                        </ResponsiveNavLink>

                        <ResponsiveNavLink :href="route('admin.stagiaires.index')" :active="route().current('admin.stagiaires.index')">
                            Stagiaires
                        </ResponsiveNavLink>

                        <ResponsiveNavLink :href="route('admin.agents.index')" :active="route().current('admin.agents.index')">
                            Entreprises
                        </ResponsiveNavLink>
                    </div>

                    <!-- Responsive Settings Options -->
                    <div class="border-t border-gray-200 pb-1 pt-4">
                        <div class="px-4">
                            <div class="text-base font-medium text-gray-800">
                                {{ $page.props.auth.user.name }}
                            </div>
                            <div class="text-sm font-medium text-gray-500">
                                {{ $page.props.auth.user.email }}
                            </div>
                        </div>

                        <div class="mt-3 space-y-1">
                            <ResponsiveNavLink :href="route('profile.edit')">
                                Profile
                            </ResponsiveNavLink>
                            <ResponsiveNavLink :href="route('logout')" method="post" as="button">
                                Log Out
                            </ResponsiveNavLink>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Page Heading -->
            <header class="bg-white shadow" v-if="$slots.header">
                <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
                    <slot name="header" />
                </div>
            </header>

            <!-- Page Content -->
            <main>
                <slot />
            </main>
        </div>
    </div>
</template>
