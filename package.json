{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build && vite build --ssr", "lint": "eslint resources/js --ext .js,.vue --ignore-path .gitignore --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@inertiajs/vue3": "^2.0.0", "@rushstack/eslint-patch": "^1.8.0", "@storybook/addon-a11y": "^9.0.3", "@storybook/addon-docs": "^9.0.3", "@storybook/addon-onboarding": "^9.0.3", "@storybook/addon-vitest": "^9.0.3", "@storybook/vue3-vite": "^9.0.3", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/server-renderer": "^3.4.0", "autoprefixer": "^10.4.12", "axios": "^1.6.4", "eslint": "^8.57.0", "eslint-plugin-storybook": "^9.0.3", "eslint-plugin-vue": "^9.23.0", "laravel-vite-plugin": "^1.0", "playwright": "^1.52.0", "postcss": "^8.4.31", "prettier": "^3.3.0", "prettier-plugin-organize-imports": "^4.0.0", "prettier-plugin-tailwindcss": "^0.6.5", "storybook": "^9.0.3", "tailwindcss": "^3.2.1", "typescript": "^5.8.3", "vite": "^6.2.6", "vitest": "^3.1.4", "vue": "^3.4.0", "vue-tsc": "^2.2.10"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@heroicons/vue": "^2.2.0", "animate.css": "^4.1.1", "chart.js": "^4.4.9", "vue-chartjs": "^5.3.2", "vue-toast-notification": "^3.1.3"}}