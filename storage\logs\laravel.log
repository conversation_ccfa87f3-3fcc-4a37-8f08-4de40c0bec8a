��
 
 [2025-07-20 18:14:17] local.INFO: DEBUG MS {"affectations":{"Illuminate\\Support\\Collection":[{"id":2,"maitre_stage_id":10,"maitre_stage":{"id":10,"user_id":10,"nom":"tbone","prenom":"mrine","email":"<EMAIL>"}}]}} 
[2025-07-20 18:14:57] local.INFO: Tentative d'accès au dashboard DPAF {"user_id":9,"user_role":"Agent","has_agent":"oui","agent_role":"RS","is_dpaf_responsable":"oui"} 
[2025-07-20 18:14:58] local.ERROR: Erreur lors de la récupération des structures {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stagiaires.structure_id' in 'where clause' (Connection: mysql, SQL: select `structures`.*, (select count(*) from `stagiaires` where `structures`.`id` = `stagiaires`.`structure_id`) as `stagiaires_count` from `structures` where `active` = 1 limit 6)"} 
[2025-07-20 18:19:55] local.INFO: DEBUG MS {"affectations":{"Illuminate\\Support\\Collection":[{"id":2,"maitre_stage_id":10,"maitre_stage":{"id":10,"user_id":10,"nom":"tbone","prenom":"mrine","email":"<EMAIL>"}}]}} 
[2025-07-20 18:21:01] local.INFO: Tentative d'accès au dashboard DPAF {"user_id":9,"user_role":"Agent","has_agent":"oui","agent_role":"RS","is_dpaf_responsable":"oui"} 
[2025-07-20 18:21:01] local.ERROR: Erreur lors de la récupération des structures {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stagiaires.structure_id' in 'where clause' (Connection: mysql, SQL: select `structures`.*, (select count(*) from `stagiaires` where `structures`.`id` = `stagiaires`.`structure_id`) as `stagiaires_count` from `structures` where `active` = 1 limit 6)"} 
[2025-07-20 18:24:32] local.ERROR: Class "App\Http\Controllers\Stagiaire\DemandeController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Stagiaire\\DemandeController\" does not exist at C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 109)
#4 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\laragon\\www\\gestion-stages-hdensac\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\laragon\\www\\gestion-stages-hdensac\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-07-20 18:28:00] local.INFO: DEBUG MS {"affectations":{"Illuminate\\Support\\Collection":[{"id":2,"maitre_stage_id":10,"maitre_stage":{"id":10,"user_id":10,"nom":"tbone","prenom":"mrine","email":"<EMAIL>"}}]}} 
[2025-07-20 18:35:30] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-20 18:35:31] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-20 19:08:31] local.INFO: Agents récupérés pour l'organigramme {"structure_id":3,"nombre_agents":0,"agents":{"Illuminate\\Database\\Eloquent\\Collection":[]}} 
[2025-07-20 19:15:32] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-20 19:15:33] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-20 19:43:21] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-20 19:43:21] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-20 19:57:42] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-20 19:57:42] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-21 01:47:58] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-21 01:47:58] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-21 04:05:33] local.INFO: Tentative d'accès au dashboard DPAF {"user_id":9,"user_role":"Agent","has_agent":"oui","agent_role":"RS","is_dpaf_responsable":"oui"} 
[2025-07-21 04:05:33] local.ERROR: Erreur lors de la récupération des structures {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stagiaires.structure_id' in 'where clause' (Connection: mysql, SQL: select `structures`.*, (select count(*) from `stagiaires` where `structures`.`id` = `stagiaires`.`structure_id`) as `stagiaires_count` from `structures` where `active` = 1 limit 6)"} 
[2025-07-21 04:13:30] local.INFO: Tentative d'accès au dashboard DPAF {"user_id":9,"user_role":"Agent","has_agent":"oui","agent_role":"RS","is_dpaf_responsable":"oui"} 
[2025-07-21 04:13:30] local.ERROR: Erreur lors de la récupération des structures {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stagiaires.structure_id' in 'where clause' (Connection: mysql, SQL: select `structures`.*, (select count(*) from `stagiaires` where `structures`.`id` = `stagiaires`.`structure_id`) as `stagiaires_count` from `structures` where `active` = 1 limit 6)"} 
[2025-07-21 04:14:07] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 04:14:07] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 04:14:07] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 04:14:07] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 04:14:07] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 04:15:48] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-21 04:26:46] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-21 04:27:18] local.INFO: DEBUG_STAGE_SHOW {"id":2,"theme_stage_id":2,"themeStage":{"App\\Models\\ThemeStage":{"id":2,"user_id":10,"stage_id":2,"propose_par":"stagiaire","intitule":"sdfsdfsdf","etat":"Validé","description":"sddfsdjfdsk","mots_cles":"djfksdfbksdfbk","created_at":"2025-07-20T09:42:05.000000Z","updated_at":"2025-07-20T09:42:05.000000Z"}}} 
[2025-07-21 04:27:18] local.INFO: Stage avec informations stagiaire {"stage_id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,"}} 
[2025-07-21 05:28:49] local.INFO: Update Profile Request {"has_file":true,"all_data":{"_method":"PATCH","nom":"tbone","prenom":"mrine","email":"<EMAIL>","telephone":"1234567896","date_de_naissance":"2025-04-30","avatar":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php2089.tmp"}},"validated_data":{"nom":"tbone","prenom":"mrine","email":"<EMAIL>","telephone":"1234567896","date_de_naissance":"2025-04-30","avatar":{"Illuminate\\Http\\UploadedFile":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\php2089.tmp"}}} 
[2025-07-21 05:28:49] local.INFO: Processing avatar file  
[2025-07-21 05:28:50] local.INFO: New avatar path: avatars/iIDKpB4PxbWqzguk6v8W75qnLOWwECDTJRAqnWKn.jpg  
[2025-07-21 05:28:50] local.INFO: User updated {"user_id":10,"avatar":"avatars/iIDKpB4PxbWqzguk6v8W75qnLOWwECDTJRAqnWKn.jpg"} 
[2025-07-21 05:28:55] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 05:28:55] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 05:28:55] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 05:28:55] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 05:28:55] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 06:22:48] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 06:22:48] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 06:22:48] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 06:22:48] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 06:22:48] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 07:39:24] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 07:39:24] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 07:39:24] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 07:39:24] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 07:39:24] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 08:00:47] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 08:00:47] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 08:00:47] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 08:00:47] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 08:00:47] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 08:36:16] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 08:36:16] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 08:36:17] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 08:36:17] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 08:36:17] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 17:47:11] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 17:47:11] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 17:47:11] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 17:47:11] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 17:47:11] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 19:03:21] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 19:03:21] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 19:03:21] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:03:21] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:03:21] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 19:19:14] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 19:19:14] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 19:19:14] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:19:14] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:19:14] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 19:41:57] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-21 19:42:21] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 19:42:21] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 19:42:21] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:42:21] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 19:42:21] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-21 20:51:44] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-21 20:51:44] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-21 20:51:44] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 20:51:44] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-21 20:51:44] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-22 03:00:59] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-22 03:00:59] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-22 03:00:59] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:00:59] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:00:59] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-22 03:02:11] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-22 03:02:11] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-22 03:02:11] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:02:11] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:02:11] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-22 03:02:47] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-22 03:03:11] local.INFO: DEBUG_STAGE_SHOW {"id":2,"theme_stage_id":2,"themeStage":{"App\\Models\\ThemeStage":{"id":2,"user_id":10,"stage_id":2,"propose_par":"stagiaire","intitule":"sdfsdfsdf","etat":"Validé","description":"sddfsdjfdsk","mots_cles":"djfksdfbksdfbk","created_at":"2025-07-20T09:42:05.000000Z","updated_at":"2025-07-20T09:42:05.000000Z"}}} 
[2025-07-22 03:03:11] local.INFO: Stage avec informations stagiaire {"stage_id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,"}} 
[2025-07-22 03:03:48] local.INFO: Informations du stagiaire {"user_id":1,"stagiaire":{"App\\Models\\Stagiaire":{"id_stagiaire":7,"user_id":1,"niveau_etude":"Licence 3","filiere":"dnvbd,vdb,","universite":"DHJGDJ","universite_id":null,"created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T09:13:04.000000Z"}},"stagiaire_id":7} 
[2025-07-22 03:03:48] local.INFO: Demandes trouvées {"nombre_demandes":4,"demandes":[{"id":3,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:55:25.000000Z","date_traitement":"2025-07-20T09:39:59.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/6jXmwMZ2XUlk0lG7y5wkwjsXQSp88DrHVP8qpPpU.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"UR1HAAHG","diplomes_path":null,"visage_path":"documents/visages/U3XaVRd2JWyljMYQtHWVrgBF7JmVBs7KN5Y1f1sG.jpg","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:55:25.000000Z","updated_at":"2025-07-20T09:39:59.000000Z"},{"id":1,"stagiaire_id":7,"structure_id":null,"nature":"Individuel","type":"Académique","statut":"Acceptée","motif_refus":null,"date_soumission":"2025-07-20T07:19:59.000000Z","date_traitement":"2025-07-20T08:11:31.000000Z","traite_par":1,"lettre_cv_path":"documents/cv/I2b3tRQkoI9KMICRYwtw4JTuJ0NnOe5k5U2ra8St.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"ZU1IZ5EW","diplomes_path":null,"visage_path":"documents/visages/ey1dBMq74FuMuXe5U8vOEOmtP3lD0ZHzemN7NMqC.jpg","structure_souhaitee":null,"date_debut":"2025-07-02T00:00:00.000000Z","date_fin":"2025-07-17T00:00:00.000000Z","created_at":"2025-07-20T07:19:59.000000Z","updated_at":"2025-07-20T08:11:31.000000Z"},{"id":2,"stagiaire_id":7,"structure_id":2,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T07:53:54.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/2wptDLlQw4GQmCAD4wM2kv8Vv6KGQQlyaKdmUtmd.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"HPJT5EU0","diplomes_path":null,"visage_path":"documents/visages/AiFPcjvMhdw08uQIBkVAxsO72NOF2KJpbeglnOI4.png","structure_souhaitee":null,"date_debut":"2025-07-01T00:00:00.000000Z","date_fin":"2025-07-10T00:00:00.000000Z","created_at":"2025-07-20T07:53:54.000000Z","updated_at":"2025-07-20T07:53:54.000000Z"},{"id":4,"stagiaire_id":7,"structure_id":1,"nature":"Individuel","type":"Académique","statut":"En attente","motif_refus":null,"date_soumission":"2025-07-20T09:13:05.000000Z","date_traitement":null,"traite_par":null,"lettre_cv_path":"documents/cv/gJpNtt7adtiFpesZhEfV5BJl8SMfFRcYXjmBJPmg.pdf","lettre_motivation_path":null,"releve_notes_path":null,"convention_stage_path":null,"assurance_path":null,"code_suivi":"4V0SY7CG","diplomes_path":null,"visage_path":"documents/visages/omPndnOnOGimOnW0os7AbYaA8VoNWqdMaZ6BCY6k.jpg","structure_souhaitee":null,"date_debut":"2025-07-20T00:00:00.000000Z","date_fin":"2025-07-27T00:00:00.000000Z","created_at":"2025-07-20T09:13:05.000000Z","updated_at":"2025-07-20T09:13:05.000000Z"}]} 
[2025-07-22 03:49:26] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-22 03:49:26] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-22 03:49:26] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:49:26] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:49:26] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-22 03:49:31] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-22 03:50:02] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-22 03:50:02] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-22 03:50:02] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:50:02] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:50:02] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
[2025-07-22 03:51:22] local.INFO: Stages avec informations stagiaires {"count":2,"stages_info":{"Illuminate\\Support\\Collection":{"1":{"id":2,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}},"0":{"id":1,"stagiaire_info":{"has_demande":true,"has_stagiaire":true,"has_user":true,"nom":"Test User","prenom":"oden","email":"<EMAIL>"}}}}} 
[2025-07-22 03:51:51] local.INFO: Update Profile Request {"has_file":false,"all_data":{"nom":"tbone","prenom":"mrine","email":"<EMAIL>","telephone":"1234567895","date_de_naissance":"2025-07-02","avatar":null},"validated_data":{"nom":"tbone","prenom":"mrine","email":"<EMAIL>","telephone":"1234567895","date_de_naissance":"2025-07-02","avatar":null}} 
[2025-07-22 03:51:51] local.INFO: User updated {"user_id":10,"avatar":null} 
[2025-07-22 03:52:40] local.INFO: Affectations récupérées {"count":2,"affectations":[{"id":1,"stage_id":1,"maitre_stage_id":10,"stage":{"id":1,"demande_stage_id":1,"demande_stage":{"id":1,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}},{"id":2,"stage_id":2,"maitre_stage_id":10,"stage":{"id":2,"demande_stage_id":3,"demande_stage":{"id":3,"stagiaire_id":7,"stagiaire":{"id":7,"user_id":1,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}}]} 
[2025-07-22 03:52:40] local.INFO: Derniers stages récupérés {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":1,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances"},"demande_stage":{"id":3,"stagiaire":{"id":7,"user":{"id":1,"nom":"Test User","prenom":"oden"}}}}]} 
[2025-07-22 03:52:40] local.INFO: Données du stagiaire trouvées {"stage_id":1,"demande_id":1,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:52:40] local.INFO: Données du stagiaire trouvées {"stage_id":2,"demande_id":3,"stagiaire_id":7,"user_id":1,"nom":"Test User","prenom":"oden"} 
[2025-07-22 03:52:40] local.INFO: Données sérialisées envoyées à la vue {"count":2,"stages":[{"id":1,"demande_stage_id":1,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-02 00:00:00","date_fin":"2025-07-17 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":1,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}},{"id":2,"demande_stage_id":3,"structure":{"id":3,"libelle":"Direction de la Planification de l'Administration et des Finances","sigle":"DPAF"},"date_debut":"2025-07-01 00:00:00","date_fin":"2025-07-10 00:00:00","statut":"Terminé","type":"academique","note":null,"est_reaffecte":false,"est_actif":true,"demandeStage":{"id":3,"nature":"Individuel","type":"Académique","stagiaire":{"id_stagiaire":7,"niveau_etude":"Licence 3","universite":"DHJGDJ","filiere":"dnvbd,vdb,","user":{"id":1,"nom":"Test User","prenom":"oden","email":"<EMAIL>","telephone":50081559}}}}]} 
